#include <iostream>
#include "../include/ai.h"
#include "../include/gamestate.h"
#include "../include/io.h"

int main(int argc, char* argv[]) {
  int hp = 5;
  std::string mode = "PVP", log_file = "tankwar.log";
  IO::parse_command_line(argc, argv, hp, mode, log_file);
  GameState state(hp, mode, log_file);
  const Direction directions[4] = {Direction::Up, Direction::Down,
                                   Direction::Left, Direction::Right};

  IO::print_message(
      "Enter Tank A position (x y dir: 0=Up 1=Down 2=Left 3=Right): ");
  int ax, ay, adir;
  std::cin >> ax >> ay >> adir;

  // Clamp coordinates to reasonable bounds
  if (ax < -10) ax = -10;
  if (ax > 30) ax = 30;
  if (ay < -10) ay = -10;
  if (ay > 30) ay = 30;

  // Validate and fix direction for Tank A
  if (adir < 0 || adir > 3) {
    IO::print_message(
        "Invalid direction for Tank A! Using default direction: Down");
    adir = 1;  // Default to Down
  }

  Direction a_dir = static_cast<Direction>(adir);

  IO::print_message(
      "Enter Tank B position (x y dir: 0=Up 1=Down 2=Left 3=Right): ");
  int bx, by, bdir;
  std::cin >> bx >> by >> bdir;

  // Clamp coordinates to reasonable bounds
  if (bx < -10) bx = -10;
  if (bx > 30) bx = 30;
  if (by < -10) by = -10;
  if (by > 30) by = 30;

  // Validate and fix direction for Tank B
  if (bdir < 0 || bdir > 3) {
    IO::print_message(
        "Invalid direction for Tank B! Using default direction: Up");
    bdir = 0;  // Default to Up
  }

  // Check for same position and adjust if necessary
  if (ax == bx && ay == by) {
    IO::print_message(
        "Warning: Tanks at same position! Moving Tank B one position to the "
        "right.");
    bx += 1;  // Simple fix: move Tank B one position to the right
  }

  Direction b_dir = static_cast<Direction>(bdir);

  state.initialize_tanks(ax, ay, a_dir, bx, by, b_dir);

  while (state.is_game_over() == false) {
    MoveCommand moveA, moveB;
    // IMPROVED: Better AI personalities
    // aiA: Balanced fighter (id, defensiveness, guardDistance, aggression, directivity)
    AI aiA(0, 0.3, 6, 0.8, 1.2);
    // aiB: Aggressive hunter (more aggressive, prefers closer combat)
    AI aiB(1, 0.1, 3, 1.0, 1.5);
    if (mode == "PVP") {
      moveA = IO::get_user_input("Player 1 (Tank A)");
      moveB = IO::get_user_input("Player 2 (Tank B)");
    }

    else if (mode == "PVE") {
      moveA = IO::get_user_input("Player 1 (Tank A)");
      moveB = aiB.decide_move(state);
    }

    else if (mode == "EVE") {
      moveA = aiA.decide_move(state);
      moveB = aiB.decide_move(state);
    }

    state.update_turn(moveA, moveB);
    IO::print_map(state);
    state.log_state();
  }
  state.log_end();
  IO::print_end(state);
  return 0;
}