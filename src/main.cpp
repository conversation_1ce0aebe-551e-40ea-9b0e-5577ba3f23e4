#include <iostream>
#include "../include/ai.h"
#include "../include/gamestate.h"
#include "../include/io.h"

int main(int argc, char* argv[]) {
  int hp = 5;
  std::string mode = "PVP", log_file = "tankwar.log";
  IO::parse_command_line(argc, argv, hp, mode, log_file);
  GameState state(hp, mode, log_file);
  const Direction directions[4] = {Direction::Up, Direction::Down,
                                   Direction::Left, Direction::Right};

  IO::print_message(
      "Enter Tank A position (x y dir: 0=Up 1=Down 2=Left 3=Right): ");
  int ax, ay, adir;
  std::cin >> ax >> ay >> adir;

  // Basic bounds checking for Tank A
  if (ax < -10 || ax > 30 || ay < -10 || ay > 30 || adir < 0 || adir > 3) {
    IO::print_message("Warning: Tank A position may be out of bounds or invalid direction.");
  }

  Direction a_dir = static_cast<Direction>(adir);

  IO::print_message(
      "Enter Tank B position (x y dir: 0=Up 1=Down 2=Left 3=Right): ");
  int bx, by, bdir;
  std::cin >> bx >> by >> bdir;

  // Basic bounds checking for Tank B
  if (bx < -10 || bx > 30 || by < -10 || by > 30 || bdir < 0 || bdir > 3) {
    IO::print_message("Warning: Tank B position may be out of bounds or invalid direction.");
  }

  // Check for same position and adjust if necessary
  if (ax == bx && ay == by) {
    IO::print_message("Warning: Tanks at same position! Moving Tank B one position to the right.");
    bx += 1;  // Simple fix: move Tank B one position to the right
  }

  Direction b_dir = static_cast<Direction>(bdir);

  state.initialize_tanks(ax, ay, a_dir, bx, by, b_dir);

  while (state.is_game_over() == false) {
    MoveCommand moveA, moveB;
    AI aiA(0, 0.5, 8, 0.1, 0.1);
    AI aiB(1, 0.2, 4, 0.5, 0.5);
    if (mode == "PVP") {
      moveA = IO::get_user_input("Player 1 (Tank A)");
      moveB = IO::get_user_input("Player 2 (Tank B)");
    }

    else if (mode == "PVE") {
      moveA = IO::get_user_input("Player 1 (Tank A)");
      moveB = aiB.decide_move(state);
    }

    else if (mode == "EVE") {
      moveA = aiA.decide_move(state);
      moveB = aiB.decide_move(state);
    }

    state.update_turn(moveA, moveB);
    IO::print_map(state);
    state.log_state();
  }
  state.log_end();
  IO::print_end(state);
  return 0;
}