//
// Created by <PERSON><PERSON> on 25-7-26.
//

#include "../include/bullet.h"

Bullet::Bullet(int init_x, int init_y, Direction dir, int own)
    : x(init_x), y(init_y), direction(dir), owner(own), turns(0) {
  // Initialize the bullet's position and direction
}

void Bullet::move() {
  // 2 meters a round
  switch (direction) {
    case Direction::Down:
      y -= 2;
      break;
    case Direction::Up:
      y += 2;
      break;
    case Direction::Left:
      x -= 2;
      break;
    case Direction::Right:
      x += 2;
      break;
  }
  turns++;
}

// Check whether a bullet is out of the map display range
bool Bullet::is_out_of_bounds(int map_size) const {
  // Use map_size to determine boundaries dynamically
  int boundary = map_size / 2 + 4;  // Allow some buffer outside map
  return (x < -boundary || x > boundary || y < -boundary || y > boundary);
}
