#include "../include/io.h"
#include <getopt.h>
#include <iomanip>
#include <iostream>

void IO::parse_command_line(int argc, char* argv[], int& hp, std::string& mode,
                            std::string& log_file) {
  static struct option long_options[] = {
      {"help", no_argument, 0, 'h'},
      {"log-file", required_argument, 0, 'l'},
      {"mode", required_argument, 0, 'm'},
      {"initial-life", required_argument, 0, 'i'},
      {0, 0, 0, 0}};

  int opt;
  while ((opt = getopt_long(argc, argv, "hl:m:i:", long_options, nullptr)) !=
         -1) {
    switch (opt) {
      case 'h':
        print_help();
        exit(0);
      case 'l':
        log_file = optarg;
        break;
      case 'm':
        mode = optarg;
        break;
      case 'i':
        hp = std::stoi(optarg);
        break;
    }
  }
}

MoveCommand IO::get_user_input(const std::string& player) {
  print_message(player + "'s turn. Enter move (forward/left/right or W/A/D): ");
  std::string input;
  std::cin >> input;

  if (input == "forward" || input == "W") return MoveCommand::Forward;
  if (input == "left" || input == "A") return MoveCommand::Left;
  if (input == "right" || input == "D")
    return MoveCommand::Right;
  else {
    print_message("Invalid input! Please try again.");
    return IO::get_user_input(player);
  }
}

void IO::print_map(const GameState& state) {
  std::cout << std::endl;
  std::cout << "Turn: " << state.get_turn() << " Map: " << state.get_map_size()
            << "x" << state.get_map_size() << std::endl;
  std::string directionA, directionB;
  switch (state.get_tankA().get_direction()) {
    case Direction::Up:
      directionA = "Up";
      break;
    case Direction::Left:
      directionA = "Left";
      break;
    case Direction::Down:
      directionA = "Down";
      break;
    case Direction::Right:
      directionA = "Right";
      break;
  }

  switch (state.get_tankB().get_direction()) {
    case Direction::Up:
      directionB = "Up";
      break;
    case Direction::Left:
      directionB = "Left";
      break;
    case Direction::Down:
      directionB = "Down";
      break;
    case Direction::Right:
      directionB = "Right";
      break;
  }

  for (int row = 25; row >= -4; --row) {
    std::cout << "|";
    for (int col = -4; col <= 25; ++col) {
      bool isBullet = false;
      char symbol = ' ';
      for (const auto& bullet : state.get_bullets()) {
        if (col == bullet.get_x() && row == bullet.get_y()) {
          isBullet = true;
          break;
        }
      }
      if (col == state.get_tankA().get_x() && row == state.get_tankA().get_y())
        symbol = 'A';
      else if (col == state.get_tankB().get_x() &&
               row == state.get_tankB().get_y())
        symbol = 'B';
      else if (isBullet)
        symbol = '*';
      else if (row < 11 - state.get_map_size() / 2 ||
               row > 10 + state.get_map_size() / 2 ||
               col < 11 - state.get_map_size() / 2 ||
               col > 10 + state.get_map_size() / 2)
        symbol = '-';
      std::cout << symbol << '|';
    }
    std::cout << std::endl;
  }

  std::cout << std::endl;
  std::cout << " | " << std::right << "Tank A direction: " << std::setw(5)
            << directionA << " | ";
  std::cout << std::right << "Tank B direction: " << std::setw(5) << directionB
            << " | " << std::endl;
  std::cout << " | " << std::right << "Tank A HP: " << std::setw(12)
            << state.get_tankA().get_hp() << " | ";
  std::cout << std::right << "Tank B HP: " << std::setw(12)
            << state.get_tankB().get_hp() << " | " << std::endl;
}

void IO::print_help() {
  std::cout << "Usage: ./tankwar [options]\n"
            << "Options:\n"
            << "  -h, --help            Print this help message\n"
            << "  -l, --log-file <file> Log file (default: tankwar.log)\n"
            << "  -m, --mode <mode>     Mode: PVP/PVE/DEMO (default: PVP)\n"
            << "  -i, --initial-life <num> Initial HP (default: 5)\n";
}

void IO::print_message(const std::string& msg) {
  std::cout << msg << std::endl;
}

void IO::print_end(const GameState& state) {
  std::cout << std::endl;
  if (state.check_win() == 0)
    std::cout << "Game ends! Tank A is the winner." << std::endl;
  else if (state.check_win() == 1)
    std::cout << "Game ends! Tank B is the winner." << std::endl;
  else if (state.check_win() == 2)
    std::cout << "Game ends! It's a draw competition." << std::endl;
}