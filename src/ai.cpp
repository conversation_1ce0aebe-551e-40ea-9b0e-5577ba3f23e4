//
// Created by <PERSON> on 25-7-30.
//
#include "../include/ai.h"
#include <cstdlib>
#include <ctime>
#include <iomanip>

// Constructor:
AI::AI(int _id, double _defensiveness, int _guardDistance, double _aggression,
       double _intensionality)
    : id(_id),
      defensiveness(_defensiveness),
      guardDistance(_guardDistance),
      aggression(_aggression),
      intensionality(_intensionality) {}

// Destructor:
AI::~AI() = default;

// Evaluate the score of a situation.
double AI::evaluateGameState(const GameState& state) {
  Tank aiTank(1, 0, 0, 5, Direction::Up);
  Tank playerTank(0, 0, 0, 5, Direction::Up);
  // get the references of the tanks:
  if (id == 1) {
    aiTank = state.get_tankB();
    playerTank = state.get_tankA();
  } else if (id == 0) {
    aiTank = state.get_tankA();
    playerTank = state.get_tankB();
  }

  // disatnce between the tanks:
  int distance = abs(aiTank.get_x() - playerTank.get_x()) +
                 abs(aiTank.get_y() - playerTank.get_y());

  // Prevent division by zero when tanks are at same position
  if (distance == 0) {
    distance = 1;  // Set minimum distance to prevent division by zero
  }

  // CRITICAL: Boundary safety evaluation (FIXED calculation!)
  int mapSize = state.get_map_size();

  // Actual boundary calculation from gamelogic.cpp:
  // Valid range: (11 - map_size/2) to (10 + map_size/2)
  int minBound = 11 - mapSize / 2;
  int maxBound = 10 + mapSize / 2;

  // Calculate distance from each boundary
  int leftDist = aiTank.get_x() - minBound;      // Distance from left boundary
  int rightDist = maxBound - aiTank.get_x();     // Distance from right boundary
  int bottomDist = aiTank.get_y() - minBound;    // Distance from bottom boundary
  int topDist = maxBound - aiTank.get_y();       // Distance from top boundary

  int aiDistanceFromBoundary = std::min(std::min(leftDist, rightDist),
                                       std::min(bottomDist, topDist));

  // Severe penalty for being outside or too close to boundaries
  double boundaryPenalty = 0.0;
  if (aiDistanceFromBoundary <= 0) {
    boundaryPenalty = -10000.0;  // Massive penalty for being outside map
  } else if (aiDistanceFromBoundary <= 1) {
    boundaryPenalty = -5000.0;  // Extremely heavy penalty for being 1 step from boundary
  } else if (aiDistanceFromBoundary <= 2) {
    boundaryPenalty = -1000.0;  // Heavy penalty for being 2 steps from boundary
  } else if (aiDistanceFromBoundary <= 3) {
    boundaryPenalty = -200.0;   // Moderate penalty for being 3 steps from boundary
  } else if (aiDistanceFromBoundary <= 4) {
    boundaryPenalty = -50.0;    // Light penalty for being 4 steps from boundary
  }

  // the difference of hp of the tanks:
  int hpDifference = aiTank.get_hp() - playerTank.get_hp();

  // FIXED: Better calculation for hitting possibility
  // Higher score when tanks are aligned (same row/column)
  int possibility = 0;
  bool sameRow = (aiTank.get_y() == playerTank.get_y());
  bool sameCol = (aiTank.get_x() == playerTank.get_x());
  if (sameRow || sameCol) {
    possibility = 10;  // High bonus for being aligned
  } else {
    // Smaller bonus for being close to alignment
    int rowDiff = abs(aiTank.get_y() - playerTank.get_y());
    int colDiff = abs(aiTank.get_x() - playerTank.get_x());
    possibility = 5 - std::min(rowDiff, colDiff);
  }

  // IMPROVED: Better directivity calculation
  // Check if AI tank is pointing towards the opponent
  double directivity = 0.0;
  int dx = playerTank.get_x() - aiTank.get_x();
  int dy = playerTank.get_y() - aiTank.get_y();

  if (aiTank.get_direction() == Direction::Up && dy > 0)
    directivity = 1.0;  // Pointing towards opponent
  else if (aiTank.get_direction() == Direction::Down && dy < 0)
    directivity = 1.0;
  else if (aiTank.get_direction() == Direction::Left && dx < 0)
    directivity = 1.0;
  else if (aiTank.get_direction() == Direction::Right && dx > 0)
    directivity = 1.0;
  else
    directivity = -0.5;  // Pointing away from opponent

  int winningChance = 0;
  if (state.check_win() == id) winningChance = 1;
  if (state.check_win() == 1 - id) winningChance = -2;
  if (state.check_win() == 2) winningChance = -1;

  // return the total score:
  // the parameters can be adjusted when initializing the AI
  return hpDifference - defensiveness * abs(distance - guardDistance) +
         aggression * possibility + intensionality * directivity +
         winningChance + boundaryPenalty;  // CRITICAL: Add boundary safety
}

// Calculate the score of a movement by using the Minimax algorithm.
// This algorithm helps determine the optimal move for a tank by
// exploring all possible moves and their outcomes in a game tree.
// The algorithm assumes that the opponent will also play optimally.
// Starting from the current game state, it recursively evaluates
// all possible moves to a certain depth. At each level, it alternates
// between maximizing (AI) and minimizing (opponent) players,
// selecting the move with the highest or lowest score respectively.
// The scores are propagated back up the tree to determine the
// optimal move for AI.
double AI::minimax(const GameState& state, int depth, bool isMaximizingPlayer,
                   MoveCommand _move) {
  // if reaching the set depth or game ends, return the evaluated score:
  if (depth == 0 || state.check_win() != -1) {
    return evaluateGameState(state);
  }

  if (isMaximizingPlayer) {
    double maxEval = -INFINITY;
    // try all three possible movements for AI tank:
    for (auto move :
         {MoveCommand::Forward, MoveCommand::Left, MoveCommand::Right}) {
      GameState newState = state;
      // FIXED: Actually apply the move to the new state
      if (id == 1)
        newState.update_turn(_move, move);  // AI is Tank B
      else if (id == 0)
        newState.update_turn(move, _move);  // AI is Tank A

      // SAFETY CHECK: Heavily penalize moves that lead to boundary damage
      Tank aiTankAfterMove = (id == 1) ? newState.get_tankB() : newState.get_tankA();
      int mapSize = newState.get_map_size();

      // FIXED: Use correct boundary calculation
      int minBound = 11 - mapSize / 2;
      int maxBound = 10 + mapSize / 2;

      // Check if AI tank would be outside boundaries after this move
      bool outsideBoundaries = (aiTankAfterMove.get_x() < minBound ||
                               aiTankAfterMove.get_x() > maxBound ||
                               aiTankAfterMove.get_y() < minBound ||
                               aiTankAfterMove.get_y() > maxBound);

      double eval;
      if (outsideBoundaries) {
        eval = -10000.0;  // Extremely bad move - avoid at all costs
      } else {
        // use recursion to get the evaluated score:
        eval = minimax(newState, depth - 1, false, move);
      }

      // take the score where best movement is taken:
      maxEval = std::max(maxEval, eval);
    }
    return maxEval;
  } else {
    double minEval = +INFINITY;
    // try all three possible movements for opponent tank:
    for (auto move :
         {MoveCommand::Forward, MoveCommand::Left, MoveCommand::Right}) {
      GameState newState = state;
      // Update the game state to see what will happen:
      if (id == 1)
        newState.update_turn(move, _move);  // Opponent is Tank A
      else if (id == 0)
        newState.update_turn(_move, move);  // Opponent is Tank B

      // SAFETY CHECK: Also consider opponent boundary safety
      Tank opponentTankAfterMove = (id == 1) ? newState.get_tankA() : newState.get_tankB();
      int mapSize = newState.get_map_size();

      // FIXED: Use correct boundary calculation
      int minBound = 11 - mapSize / 2;
      int maxBound = 10 + mapSize / 2;

      // Check if opponent tank would be outside boundaries after this move
      bool outsideBoundaries = (opponentTankAfterMove.get_x() < minBound ||
                               opponentTankAfterMove.get_x() > maxBound ||
                               opponentTankAfterMove.get_y() < minBound ||
                               opponentTankAfterMove.get_y() > maxBound);

      double eval;
      if (outsideBoundaries) {
        eval = 10000.0;  // Good for AI if opponent makes bad move
      } else {
        // use recursion to get the evaluated score:
        eval = minimax(newState, depth - 1, true, move);
      }

      // take the score where the opponent tries to minimize the score of AI:
      minEval = std::min(minEval, eval);
    }
    return minEval;
  }
}

// Decide next movement for the AI tank.
MoveCommand AI::decide_move(const GameState& state) {
  double bestValue = -INFINITY;
  double values[3];  // store the Minimax for all three movements
  int i = 0;
  const MoveCommand MoveCommands[3] = {MoveCommand::Forward, MoveCommand::Left,
                                       MoveCommand::Right};
  MoveCommand bestMove = MoveCommand::Forward;

  // calculate the Minimax for all three movements:
  for (auto move :
       {MoveCommand::Forward, MoveCommand::Left, MoveCommand::Right}) {
    GameState newState = state;
    // IMPROVED: Use depth 4 for better performance while maintaining good strategy
    double moveValue = minimax(newState, 4, false, move);

    if (moveValue > bestValue) {
      bestValue = moveValue;
      bestMove = move;
    }  // take the move with highest score

    values[i] = moveValue;
    i++;
  }

  // if more than one movement have the highest score, randomly take one of them
  if (bestValue == values[0] && bestValue == values[1] &&
      bestValue == values[2])
    bestMove = MoveCommands[rand() % 3];
  else if (bestValue == values[0] && bestValue == values[1])
    bestMove = MoveCommands[rand() % 2];
  else if (bestValue == values[1] && bestValue == values[2])
    bestMove = MoveCommands[rand() % 2 + 1];
  else if (bestValue == values[0] && bestValue == values[2])
    bestMove = MoveCommands[(rand() % 2) * 2];

  return bestMove;
}