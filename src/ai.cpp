//
// Created by <PERSON> on 25-7-30.
//
#include "../include/ai.h"
#include <cstdlib>
#include <ctime>
#include <iomanip>

// Constructor:
AI::AI(int _id, double _defensiveness, int _guardDistance, double _aggression,
       double _intensionality)
    : id(_id),
      defensiveness(_defensiveness),
      guardDistance(_guardDistance),
      aggression(_aggression),
      intensionality(_intensionality) {}

// Destructor:
AI::~AI() = default;

// Evaluate the score of a situation.
double AI::evaluateGameState(const GameState& state) {
  Tank aiTank(1, 0, 0, 5, Direction::Up);
  Tank playerTank(0, 0, 0, 5, Direction::Up);
  // get the references of the tanks:
  if (id == 1) {
    aiTank = state.get_tankB();
    playerTank = state.get_tankA();
  } else if (id == 0) {
    aiTank = state.get_tankA();
    playerTank = state.get_tankB();
  }

  // disatnce between the tanks:
  int distance = abs(aiTank.get_x() - playerTank.get_x()) +
                 abs(aiTank.get_y() - playerTank.get_y());

  // Prevent division by zero when tanks are at same position
  if (distance == 0) {
    distance = 1;  // Set minimum distance to prevent division by zero
  }

  // the difference of hp of the tanks:
  int hpDifference = aiTank.get_hp() - playerTank.get_hp();
  // the possibility of hitting the oppponent (determined by whether they are on
  // the same line or close lines):
  int possibility = -std::min(abs(aiTank.get_x() - playerTank.get_x()),
                              abs(aiTank.get_y() - playerTank.get_y()));
  // the directivity (determined by whether the tank is pointing to the
  // opponent):
  double directivity = 0.0;  // Initialize to prevent undefined behavior
  if (aiTank.get_direction() == Direction::Up)
    directivity = (playerTank.get_y() - aiTank.get_y()) / (double)distance;
  else if (aiTank.get_direction() == Direction::Down)
    directivity = (aiTank.get_y() - playerTank.get_y()) / (double)distance;
  else if (aiTank.get_direction() == Direction::Left)
    directivity = (aiTank.get_x() - playerTank.get_x()) / (double)distance;
  else if (aiTank.get_direction() == Direction::Right)  // Fixed: was Left twice
    directivity = (playerTank.get_x() - aiTank.get_x()) / (double)distance;

  int winningChance = 0;
  if (state.check_win() == id) winningChance = 1;
  if (state.check_win() == 1 - id) winningChance = -2;
  if (state.check_win() == 2) winningChance = -1;

  // return the total score:
  // the parameters can be adjusted when initializing the AI
  return hpDifference - defensiveness * abs(distance - guardDistance) +
         aggression * possibility + intensionality * directivity +
         winningChance;
}

// Calculate the score of a movement by using the Minimax algorithm.
// This algorithm helps determine the optimal move for a tank by
// exploring all possible moves and their outcomes in a game tree.
// The algorithm assumes that the opponent will also play optimally.
// Starting from the current game state, it recursively evaluates
// all possible moves to a certain depth. At each level, it alternates
// between maximizing (AI) and minimizing (opponent) players,
// selecting the move with the highest or lowest score respectively.
// The scores are propagated back up the tree to determine the
// optimal move for AI.
double AI::minimax(const GameState& state, int depth, bool isMaximizingPlayer,
                   MoveCommand _move) {
  if (isMaximizingPlayer) {
    // if reaching the set depth or game ends, return the evaluated score:
    if (depth == 0 || state.check_win() != -1) {
      return evaluateGameState(state);
    }
    double maxEval = -INFINITY;
    // try all three possible movements for AI tank:
    for (auto move :
         {MoveCommand::Forward, MoveCommand::Left, MoveCommand::Right}) {
      GameState newState = state;
      // use recursion to get the evaluated score:
      double eval = minimax(newState, depth, false, move);
      // take the score where best movement is taken:
      maxEval = std::max(maxEval, eval);
    }
    return maxEval;
  } else {
    double minEval = +INFINITY;
    // try all three possible movements for opponent tank:
    for (auto move :
         {MoveCommand::Forward, MoveCommand::Left, MoveCommand::Right}) {
      GameState newState = state;
      // Update the game state to see what will happen:
      if (id == 1)
        newState.update_turn(move, _move);
      else if (id == 0)
        newState.update_turn(_move, move);
      // use recursion to get the evaluated score:
      double eval = minimax(newState, depth - 1, true, move);
      // take the score where the opponent tries to minimize the score of AI:
      minEval = std::min(minEval, eval);
    }
    return minEval;
  }
}

// Decide next movement for the AI tank.
MoveCommand AI::decide_move(const GameState& state) {
  double bestValue = -INFINITY;
  double values[3];  // store the Minimax for all three movements
  int i = 0;
  const MoveCommand MoveCommands[3] = {MoveCommand::Forward, MoveCommand::Left,
                                       MoveCommand::Right};
  MoveCommand bestMove = MoveCommand::Forward;

  // calculate the Minimax for all three movements:
  for (auto move :
       {MoveCommand::Forward, MoveCommand::Left, MoveCommand::Right}) {
    GameState newState = state;
    double moveValue = minimax(newState, 6, false, move);

    if (moveValue > bestValue) {
      bestValue = moveValue;
      bestMove = move;
    }  // take the move with highest score

    values[i] = moveValue;
    i++;
  }

  // if more than one movement have the highest score, randomly take one of them
  if (bestValue == values[0] && bestValue == values[1] &&
      bestValue == values[2])
    bestMove = MoveCommands[rand() % 3];
  else if (bestValue == values[0] && bestValue == values[1])
    bestMove = MoveCommands[rand() % 2];
  else if (bestValue == values[1] && bestValue == values[2])
    bestMove = MoveCommands[rand() % 2 + 1];
  else if (bestValue == values[0] && bestValue == values[2])
    bestMove = MoveCommands[(rand() % 2) * 2];

  return bestMove;
}