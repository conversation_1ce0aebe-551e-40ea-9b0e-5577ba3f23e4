//
// Created by <PERSON><PERSON> on 25-7-26.
//

#ifndef BULLET_H
#define BULLET_H
#include "tank.h"
class Bullet {
 private:
  int x, y;
  Direction direction;
  int owner;  // 0 for A, 1 for B
  int turns;  // turn number that this bullet has been existing

 public:
  Bullet(int init_x, int init_y, Direction dir, int own);

  void move();  // 2 meters each turn

  [[nodiscard]] bool is_out_of_bounds(int map_size) const;  // checker
  // getters
  [[nodiscard]] int get_x() const { return x; }
  [[nodiscard]] int get_y() const { return y; }
  [[nodiscard]] Direction get_direction() const { return direction; }
  [[nodiscard]] int get_owner() const { return owner; }
  [[nodiscard]] int get_turns() const { return turns; }
};
#endif  // BULLET_H
